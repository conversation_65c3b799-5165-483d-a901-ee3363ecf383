package api

import (
	"time"

	"kids-platform/internal/converters"
	"kids-platform/internal/models"
	"kids-platform/internal/repositories"
	"kids-platform/pkg/errcode"
	"kids-platform/pkg/logger"
)

// ChildrenService 孩子管理服务接口
type ChildrenService interface {
	// 基础CRUD操作
	CreateChild(userID uint, req *models.ChildrenCreateRequest) (*models.ChildrenResponse, error)
	GetChildByID(userID, childID uint) (*models.ChildrenResponse, error)
	UpdateChild(userID, childID uint, req *models.ChildrenUpdateRequest) (*models.ChildrenResponse, error)
	DeleteChild(userID, childID uint) error

	// 孩子列表和查询
	GetChildrenByUserID(userID uint) ([]*models.ChildrenResponse, error)
	GetCurrentChild(userID uint) (*models.ChildrenResponse, error)

	// 孩子选择和管理
	SelectCurrentChild(userID, childID uint) error
	AddChildToUser(userID, childID uint, relation string) error
	RemoveChildFromUser(userID, childID uint) error

	// 权限检查
	CheckUserCanManageChild(userID, childID uint) (bool, error)
}

// childrenService 孩子管理服务实现
type childrenService struct {
	childrenRepo     repositories.ChildrenRepository
	userChildrenRepo repositories.UserChildrenRepository
	userRepo         repositories.UsersRepository
}

// NewChildrenService 创建孩子管理服务
func NewChildrenService(
	childrenRepo repositories.ChildrenRepository,
	userChildrenRepo repositories.UserChildrenRepository,
	userRepo repositories.UsersRepository,
) ChildrenService {
	return &childrenService{
		childrenRepo:     childrenRepo,
		userChildrenRepo: userChildrenRepo,
		userRepo:         userRepo,
	}
}

// CreateChild 创建孩子档案
func (s *childrenService) CreateChild(userID uint, req *models.ChildrenCreateRequest) (*models.ChildrenResponse, error) {
	// 1. 创建孩子档案
	child := &models.Children{
		Name:                req.Name,
		Nickname:            req.Nickname,
		Gender:              req.Gender,
		School:              req.School,
		Grade:               req.Grade,
		Province:            req.Province,
		City:                req.City,
		Avatar:              req.Avatar,
		SkillLevel:          1, // 默认新手级别
		PreferredDifficulty: 1, // 默认简单难度
		PrivacyLevel:        2, // 默认好友可见
		ShowInLeaderboard:   1, // 默认参与排行榜
		Status:              1, // 正常状态
	}

	// 处理出生日期
	if req.BirthDate != "" {
		if birthDate, err := time.Parse("2006-01-02", req.BirthDate); err == nil {
			child.BirthDate = &birthDate
		}
	}

	if err := s.childrenRepo.Create(child); err != nil {
		logger.Error("Failed to create child", "user_id", userID, "error", err)
		return nil, err
	}

	// 2. 创建用户孩子关联
	userChild := &models.UserChildren{
		UserID:   uint64(userID),
		ChildID:  uint64(child.ID),
		Relation: req.Relation,
	}

	if err := s.userChildrenRepo.Create(userChild); err != nil {
		logger.Error("Failed to create user-child relation", "user_id", userID, "child_id", child.ID, "error", err)
		// 如果关联创建失败，删除已创建的孩子档案
		s.childrenRepo.Delete(child.ID)
		return nil, err
	}

	// 3. 如果用户当前没有选择孩子，自动选择新创建的孩子
	user, err := s.userRepo.GetByID(userID)
	if err == nil && user.CurrentChildID == 0 {
		s.userRepo.Update(userID, &models.Users{CurrentChildID: uint64(child.ID)})
	}

	logger.Info("Child created successfully", "user_id", userID, "child_id", child.ID)
	return child.ToResponse(), nil
}

// GetChildByID 获取孩子详情
func (s *childrenService) GetChildByID(userID, childID uint) (*models.ChildrenResponse, error) {
	// 1. 检查用户权限
	canManage, err := s.CheckUserCanManageChild(userID, childID)
	if err != nil {
		return nil, err
	}
	if !canManage {
		return nil, errcode.ErrForbidden.WithDetails("无权限访问该孩子信息")
	}

	// 2. 获取孩子信息
	child, err := s.childrenRepo.GetByID(childID)
	if err != nil {
		return nil, err
	}

	return child.ToResponse(), nil
}

// UpdateChild 更新孩子信息
func (s *childrenService) UpdateChild(userID, childID uint, req *models.ChildrenUpdateRequest) (*models.ChildrenResponse, error) {
	// 1. 检查用户权限
	canManage, err := s.CheckUserCanManageChild(userID, childID)
	if err != nil {
		return nil, err
	}
	if !canManage {
		return nil, errcode.ErrForbidden.WithDetails("无权限修改该孩子信息")
	}

	// 2. 构建更新数据
	updateData := &models.Children{
		Name:                req.Name,
		Nickname:            req.Nickname,
		Gender:              req.Gender,
		School:              req.School,
		Grade:               req.Grade,
		Province:            req.Province,
		City:                req.City,
		Avatar:              req.Avatar,
		PreferredDifficulty: req.PreferredDifficulty,
		PrivacyLevel:        req.PrivacyLevel,
		ShowInLeaderboard:   req.ShowInLeaderboard,
	}

	// 处理出生日期
	if req.BirthDate != "" {
		if birthDate, err := time.Parse("2006-01-02", req.BirthDate); err == nil {
			updateData.BirthDate = &birthDate
		}
	}

	// 3. 更新孩子信息
	if err := s.childrenRepo.Update(childID, updateData); err != nil {
		logger.Error("Failed to update child", "user_id", userID, "child_id", childID, "error", err)
		return nil, err
	}

	// 4. 获取更新后的孩子信息
	child, err := s.childrenRepo.GetByID(childID)
	if err != nil {
		return nil, err
	}

	logger.Info("Child updated successfully", "user_id", userID, "child_id", childID)
	return child.ToResponse(), nil
}

// DeleteChild 删除孩子档案
func (s *childrenService) DeleteChild(userID, childID uint) error {
	// 1. 检查用户权限
	canManage, err := s.CheckUserCanManageChild(userID, childID)
	if err != nil {
		return err
	}
	if !canManage {
		return errcode.ErrForbidden.WithDetails("无权限删除该孩子信息")
	}

	// 2. 检查是否为当前选择的孩子，如果是则重置用户的current_child_id
	user, err := s.userRepo.GetByID(userID)
	if err == nil && user.CurrentChildID == uint64(childID) {
		s.userRepo.Update(userID, &models.Users{CurrentChildID: 0})
	}

	// 3. 软删除孩子档案
	if err := s.childrenRepo.Delete(childID); err != nil {
		logger.Error("Failed to delete child", "user_id", userID, "child_id", childID, "error", err)
		return err
	}

	// 4. 软删除相关的用户孩子关联
	if err := s.userChildrenRepo.DeleteByUserAndChild(userID, childID); err != nil {
		logger.Error("Failed to delete user-child relation", "user_id", userID, "child_id", childID, "error", err)
		// 不影响主要删除流程
	}

	logger.Info("Child deleted successfully", "user_id", userID, "child_id", childID)
	return nil
}

// GetChildrenByUserID 获取用户管理的所有孩子
func (s *childrenService) GetChildrenByUserID(userID uint) ([]*models.ChildrenResponse, error) {
	children, err := s.childrenRepo.GetByUserID(userID)
	if err != nil {
		logger.Error("Failed to get children by user ID", "user_id", userID, "error", err)
		return nil, err
	}

	// 使用转换器将列表转换为响应格式
	converter := converters.NewChildrenConverter()
	return converter.ToResponseList(children), nil
}

// GetCurrentChild 获取用户当前选择的孩子
func (s *childrenService) GetCurrentChild(userID uint) (*models.ChildrenResponse, error) {
	child, err := s.childrenRepo.GetCurrentChildByUserID(userID)
	if err != nil {
		return nil, err
	}

	return child.ToResponse(), nil
}

// SelectCurrentChild 选择当前孩子
func (s *childrenService) SelectCurrentChild(userID, childID uint) error {
	// 1. 检查用户权限
	canManage, err := s.CheckUserCanManageChild(userID, childID)
	if err != nil {
		return err
	}
	if !canManage {
		return errcode.ErrForbidden.WithDetails("无权限选择该孩子")
	}

	// 2. 更新用户的current_child_id
	if err := s.userRepo.Update(userID, &models.Users{CurrentChildID: uint64(childID)}); err != nil {
		logger.Error("Failed to select current child", "user_id", userID, "child_id", childID, "error", err)
		return err
	}

	logger.Info("Current child selected successfully", "user_id", userID, "child_id", childID)
	return nil
}

// AddChildToUser 将孩子添加到用户管理列表
func (s *childrenService) AddChildToUser(userID, childID uint, relation string) error {
	// 1. 检查孩子是否存在
	_, err := s.childrenRepo.GetByID(childID)
	if err != nil {
		return err
	}

	// 2. 检查是否已经存在关联
	existing, _ := s.userChildrenRepo.GetByUserAndChild(userID, childID)
	if existing != nil {
		return errcode.ErrDataExists.WithDetails("用户已经管理该孩子")
	}

	// 3. 创建关联
	userChild := &models.UserChildren{
		UserID:   uint64(userID),
		ChildID:  uint64(childID),
		Relation: relation,
	}

	if err := s.userChildrenRepo.Create(userChild); err != nil {
		logger.Error("Failed to add child to user", "user_id", userID, "child_id", childID, "error", err)
		return err
	}

	logger.Info("Child added to user successfully", "user_id", userID, "child_id", childID)
	return nil
}

// RemoveChildFromUser 从用户管理列表中移除孩子
func (s *childrenService) RemoveChildFromUser(userID, childID uint) error {
	// 1. 检查是否为当前选择的孩子，如果是则重置
	user, err := s.userRepo.GetByID(userID)
	if err == nil && user.CurrentChildID == uint64(childID) {
		s.userRepo.Update(userID, &models.Users{CurrentChildID: 0})
	}

	// 2. 删除关联
	if err := s.userChildrenRepo.DeleteByUserAndChild(userID, childID); err != nil {
		logger.Error("Failed to remove child from user", "user_id", userID, "child_id", childID, "error", err)
		return err
	}

	logger.Info("Child removed from user successfully", "user_id", userID, "child_id", childID)
	return nil
}

// CheckUserCanManageChild 检查用户是否可以管理某个孩子
func (s *childrenService) CheckUserCanManageChild(userID, childID uint) (bool, error) {
	return s.userChildrenRepo.CheckUserCanManageChild(userID, childID)
}
